<script setup lang="ts">
import Button from 'primevue/button'
import { ref } from 'vue'
import UserSelectDialog from '../config/UserSelectDialog.vue'
import { useFeedbackTriggerRecordCreateForm } from './schema'
import { TriggerRecordApi } from '~/api/feedback/trigger'
import { feedbackConfigApi } from '~/api/feedback/config'
import type { NoticeUser } from '~/api/feedback/trigger/type'
import type { User } from '~/api/wx-server/types'
import { error, success } from '~/composables/toast'
import { useDictStore } from '~/stores/dict'

const emits = defineEmits<{ save: [] }>()
const loading = ref(false)
const open = defineModel<boolean>('open')

// 用户选择相关状态
const userSelectOpen = ref(false)
const selectedNoticeUsers = ref<NoticeUser[]>([])

const { handleSubmit, resetForm, setFieldValue } = useFeedbackTriggerRecordCreateForm()
const { errorMessage } = useField('noticeUsers')

// 字典存储和异常大类监听
const dictStore = useDictStore()
const { value: anomaliesCodeValue } = useField('anomaliesCode')

// 监听异常大类选择变化，自动设置异常名称
watch(anomaliesCodeValue, (newCode) => {
  if (newCode && typeof newCode === 'string') {
    const anomaliesName = dictStore.getLabel('ABNORMAL_CATEGORY', newCode)
    setFieldValue('anomaliesName', anomaliesName || '')
  }
  else {
    setFieldValue('anomaliesName', '')
  }
})

const trigger = handleSubmit(async (values) => {
  try {
    loading.value = true

    // 校验线体编码和异常大类是否存在
    const config = await feedbackConfigApi.findOne(values.lineCode, values.anomaliesCode)
    if (!config) {
      error('不存在对应线体编码和异常大类的快返配置，无法触发！！！')
      return
    }

    await TriggerRecordApi.create(values)
    success('异常已触发')
    emits('save')
    open.value = false
    resetForm()
  }
  finally {
    loading.value = false
  }
})

// 用户选择处理
function handleUserSelected(users: User[]) {
  selectedNoticeUsers.value = users.map(user => ({
    noticeUserId: user.userid,
    noticeUserName: user.name,
  }))
  setFieldValue('noticeUsers', selectedNoticeUsers.value)
}

// 移除选中的通知用户
function removeNoticeUser(index: number) {
  selectedNoticeUsers.value.splice(index, 1)
  setFieldValue('noticeUsers', selectedNoticeUsers.value)
}

// 打开用户选择对话框
function openUserSelect() {
  userSelectOpen.value = true
}

// 获取当前选中的用户（用于传递给UserSelectDialog）
const currentSelectedUsers = computed(() => {
  return selectedNoticeUsers.value.map(noticeUser => ({
    userid: noticeUser.noticeUserId,
    name: noticeUser.noticeUserName || '',
    department: [],
  }))
})

function initializeForm() {
  resetForm()
  selectedNoticeUsers.value = []
  setFieldValue('anomaliesName', '')
}
</script>

<template>
  <Dialog v-model:visible="open" modal header="异常响应触发" :style="{ width: '50rem' }" @show="initializeForm">
    <form @submit.prevent="trigger">
      <div class="space-y-6">
        <LDictSelect name="lineCode" label="线体编码" code="LINE_CODE" />
        <LDatePicker name="anomaliesStartTime" label="异常开始时间" :date-props="{ showTime: true, showSeconds: true }" />
        <LDictSelect name="anomaliesCode" label="异常大类" code="ABNORMAL_CATEGORY" />
        <LTextarea name="anomaliesDetail" label="异常描述" />

        <!-- 通知用户选择 -->
        <div class="space-y-2">
          <label class="block text-sm font-medium">
            异常解决时，通知用户 <span class="text-red-500">*</span>
          </label>
          <div class="flex items-center gap-2">
            <Button type="button" icon="pi pi-user-plus" label="选择通知用户" outlined @click="openUserSelect" />
            <span class="text-sm text-surface-500 dark:text-surface-400">已选择 {{ selectedNoticeUsers.length }} 人</span>
          </div>

          <!-- 已选用户显示区域 -->
          <div v-if="selectedNoticeUsers.length > 0" class="h-32 overflow-auto border-1 rounded-md p-2 border-surface">
            <div class="space-y-1">
              <div v-for="(user, index) in selectedNoticeUsers" :key="user.noticeUserId">
                <Chip
                  :label="user.noticeUserName || user.noticeUserId" class="w-full justify-between" removable
                  @remove="removeNoticeUser(index)"
                >
                  <span class="text-sm">{{ user.noticeUserName || user.noticeUserId }}</span>
                </Chip>
              </div>
            </div>
          </div>
          <div v-else class="h-32 flex items-center justify-center border-1 rounded-md border-dashed border-surface">
            <span class="text-surface-400 dark:text-surface-500">请选择通知用户</span>
          </div>

          <ErrorMsg :error-message="errorMessage" />
        </div>
      </div>
      <div class="mt-8 flex justify-center gap-8">
        <Button type="submit" :loading="loading" label="触发" icon="pi pi-bolt" />
        <Button type="button" label="重置" icon="pi pi-refresh" severity="secondary" @click="resetForm()" />
      </div>
    </form>
  </Dialog>

  <!-- 用户选择对话框 -->
  <UserSelectDialog
    v-model:open="userSelectOpen" :initial-selected-users="currentSelectedUsers"
    @select="handleUserSelected"
  />
</template>
