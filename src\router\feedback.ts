import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/feedback/config',
    name: 'feedback-config',
    component: () => import('~/pages/feedback/config/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/trigger',
    name: 'feedback-trigger',
    component: () => import('~/pages/feedback/trigger/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/trigger-send',
    name: 'feedback-trigger-send',
    component: () => import('~/pages/feedback/trigger-send/index.vue'),
    meta: {},
  },
  {
    path: '/feedback/handle/:mode/:triggerRecordId/:triggerSendId',
    name: 'feedback-handle',
    component: () => import('~/pages/feedback/handle/index.vue'),
    props: true,
    meta: {
      noLayout: true,
    },
  },
] satisfies RouteRecordRaw[]
