export interface FeedbackTriggerSolution {
  /**
   * 主键ID
   */
  id: string
  /**
   * 解决方案
   */
  solution: string
  /**
   * 解决人ID
   */
  solverId: string
  /**
   * 解决人名称
   */
  solverName: string
  /**
   * 解决时间
   */
  solveTime: string
  /**
   * 触发记录ID
   */
  triggerRecordId: string
  /**
   * 发送记录ID
   */
  triggerSendId: string
  /**
   * 是否已提交
   */
  submitted?: boolean
  /**
   * 提交时间
   */
  submitTime?: string
}

export interface FeedbackTriggerSolutionCreateParam {
  /**
   * 解决方案内容
   */
  solution: string
  /**
   * 解决时间
   */
  solveTime: Date
  /**
   * 触发记录ID
   */
  triggerRecordId: string
  /**
   * 发送记录ID
   */
  triggerSendId: string
  /**
   * 解决人ID
   */
  solverId?: string
  /**
   * 解决人名称
   */
  solverName?: string
}

export interface FeedbackTriggerUpdateParam {
  /**
   * 解决方案内容
   */
  solution: string
  /**
   * 解决时间
   */
  solveTime: Date
  triggerRecordId: string
  triggerSendId: string
  solverId: string
  solverName: string
}

export interface FeedbackTriggerSearchParam {
  /**
   * 权限模式：view（仅查看）、edit（可编辑）、close（关闭异常）
   */
  mode: 'view' | 'edit' | 'close'
  /**
   * 触发记录ID
   */
  triggerRecordId: string
  /**
   * 发送记录ID
   */
  triggerSendId: string
  userId?: string
  userName?: string
}
